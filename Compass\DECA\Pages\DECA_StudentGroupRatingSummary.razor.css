.deca-rating-summary-title {
    text-align: center;
    margin-block: 2rem 4rem;
    color: var(--c4l-primary-purple);
}

.loading-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 20rem;
}

.ratings-container {
    width: 100%;
    background-color: var(--white);
    border: 1px solid var(--c4l-primary-purple);
    border-radius: 0.25rem;
    overflow: auto;
    box-shadow: 0 0.25rem 0.375rem rgba(0, 0, 0, 0.1);
}

.ratings-header {
    display: flex;
    background-color: var(--c4l-primary-purple);
    color: var(--white);
    font-weight: 600;
    border-bottom: 1px solid var(--c4l-primary-purple);
}

.ratings-header .header-item {
    flex: 1;
    padding: 1rem 0.75rem;
    text-align: center;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 8rem;
}

.ratings-header .header-item:last-child {
    border-right: none;
}

.ratings-content {
    display: flex;
    flex-direction: column;
}

.rating-row {
    display: flex;
    border-bottom: 1px solid var(--neutral-200);
    transition: background-color 0.2s ease;
}

.rating-row:hover {
    background-color: var(--neutral-100);
}

.rating-row:last-child {
    border-bottom: none;
}

.rating-item {
    flex: 1;
    padding: 0.75rem;
    text-align: center;
    border-right: 1px solid var(--neutral-200);
    min-width: 8rem;
    word-wrap: break-word;
}

.rating-item:last-child {
    border-right: none;
}

.no-data-message {
    max-width: 45rem;
    margin: 2rem auto;
    text-align: center;
    border-radius: 0.25rem;
    padding: 1.5rem;
}

.no-data-message p {
    margin: 0;
    margin-left: 0.5rem;
    display: inline;
}

.no-data-message i {
    vertical-align: middle;
}

@media (max-width: 75rem) {
    .ratings-container {
        font-size: 0.875rem;
    }

    .ratings-header .header-item,
    .rating-item {
        min-width: 6rem;
        padding: 0.5rem;
    }

    .header-cell,
    .data-cell,
    .header-subcell,
    .data-subcell {
        padding: 0.375rem;
        font-size: 0.75rem;
    }

    .header-group-title {
        font-size: 0.75rem;
        padding: 0.375rem;
    }
}

@media (max-width: 48rem) {
    .ratings-container {
        font-size: 0.75rem;
    }

    .ratings-header .header-item,
    .rating-item {
        min-width: 4rem;
        padding: 0.375rem 0.25rem;
    }

    .deca-rating-summary-title {
        font-size: 1.5rem;
        margin-block: 1rem 2rem;
    }

    .roster-header-section {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .search-input {
        width: 100%;
    }

    .header-cell,
    .data-cell,
    .header-subcell,
    .data-subcell {
        padding: 0.25rem;
        font-size: 0.625rem;
        min-height: 2rem;
    }

    .header-group-title {
        font-size: 0.625rem;
        padding: 0.25rem;
    }

    .student-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .student-summary {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        width: 100%;
    }
}

.class-roster {
    padding: 1.25rem;
}

.roster-header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.25rem;
}

.class-roster-title {
    color: var(--c4l-primary-purple);
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.search-container {
    display: flex;
}

.search-input {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--neutral-300);
    border-radius: 0.25rem;
    width: 18.75rem;
    font-size: 1rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--c4l-primary-purple);
    box-shadow: 0 0 0 0.125rem rgba(120, 53, 120, 0.25);
}

.roster-row {
    border: 1px solid var(--neutral-200);
    border-radius: 0.25rem;
    margin-bottom: 1rem;
    background-color: var(--white);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.1);
}

.student-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    cursor: pointer;
    background-color: var(--neutral-200);
    color: #61686B;
    border-radius: 0.25rem 0.25rem 0 0;
    transition: background-color 0.2s ease;
}

.student-header:hover {
    background-color: var(--c4l-primary-600);
}

.student-header:focus {
    outline: 0.125rem solid var(--white);
    outline-offset: -0.125rem;
}

.student-info {
    display: flex;
    gap: 1.25rem;
    align-items: center;
}



.rating-details {
    padding: 0;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
}

.rating-details.expanded {
    max-height: 62.5rem;
    padding: 1rem;
}

.ratings-grid-header {
    display: flex;
    background-color: var(--neutral-200);
    color: #61686B;
    font-weight: 500;
    margin-top: 0.625rem;
}

.header-cell {
    flex: 1;
    padding: 0.5rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 3rem;
    color: #61686B;
    font-weight: 700;
    text-transform: uppercase;
}

.header-group {
    flex: 5;
    display: flex;
    flex-direction: column;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.header-group:last-child {
    border-right: none;
}

.header-group-title {
    padding: 0.5rem;
    text-align: center;
    font-size: 0.875rem;
    font-weight: 700;
    margin: 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background-color: var(--neutral-200);
    color: #61686B;
    text-transform: uppercase;
}

.header-subgroup {
    display: flex;
    flex: 1;
}

.header-subcell {
    flex: 1;
    padding: 0.5rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #61686B;
    font-weight: 700;
    text-transform: uppercase;
}

.ratings-grid-content {
    display: flex;
    flex-direction: column;
}

.rating-data-row {
    display: flex;
    transition: background-color 0.2s ease;
}

.rating-data-row:hover {
    background-color: var(--neutral-100);
}

.data-cell {
    flex: 1;
    padding: 0.5rem;
    text-align: center;
    border: 1px solid var(--neutral-200);
    font-size: 0.875rem;
    word-wrap: break-word;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 2.5rem;
    color: #61686B;
}

.data-group {
    flex: 5;
    display: flex;
}

.data-subcell {
    flex: 1;
    padding: 0.5rem;
    text-align: center;
    border: 1px solid var(--neutral-200);
    font-size: 0.875rem;
    word-wrap: break-word;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 2.5rem;
    color: #61686B;
}

.view-child-btn {
    padding: 0.375rem 0.75rem;
    background-color: var(--white);
    border: 1px solid var(--c4l-secondary-teal);
    color: var(--c4l-secondary-teal);
    border-radius: 0.25rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.view-child-btn:hover {
    background-color: var(--c4l-secondary-teal);
    color: var(--white);
}

.view-child-btn:focus {
    outline: 0.125rem solid var(--c4l-secondary-teal);
    outline-offset: 0.125rem;
}

.rating-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.action-btn {
    background: none;
    border: none;
    color: var(--neutral-500);
    cursor: pointer;
    padding: 0.25rem;
    transition: color 0.2s ease;
    border-radius: 0.25rem;
}

.action-btn:hover {
    color: var(--c4l-primary-purple);
}

.action-btn:focus {
    outline: 0.125rem solid var(--c4l-primary-purple);
    outline-offset: 0.125rem;
}
